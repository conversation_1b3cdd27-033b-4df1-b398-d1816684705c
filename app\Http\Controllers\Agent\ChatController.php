<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Jobs\BulkInsertTaskJob;
use App\Models\AgentContactRelationModel;
use App\Models\AgentDeviceRelationModel;
use App\Models\Blacklist;
use App\Models\Contact;
use App\Models\Task;
use Illuminate\Http\Request;
use App\Models\Device;
use App\Models\Messages;
use App\Models\Tag;
use App\Models\Template;
use App\Models\User;
use App\Models\UserNoteModel;
use Exception;
use App\Traits\Whatsapp;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class ChatController extends Controller
{
    use Whatsapp;

    protected $user_id;

    public function __construct()
    {
        $this->middleware('auth');

        $this->middleware(function ($request, $next) {
            if (Auth::user() && Auth::user()->parent) {
                $this->user_id = Auth::user()->parent->id;
            } else {
                $this->user_id = Auth::id();
            }

            return $next($request);
        });
    }

    public function chats($id)
    {
        try {
            if ($id === null) {
                Log::warning('Device lookup attempted with null ID');
                return response()->json(['error' => 'Invalid device ID'], 400);
            }
            // dd('hellow',$this->user_id);
            $device = Device::where("user_id", $this->user_id)
                ->where("status", 1)
                ->where("uuid", $id)
                ->first();
            abort_if(empty($device), 404);

            $templates_data = getFacebookTemplates($device->waid, $device->token);

            $agentData = User::where('status', 1)->where('role', 'agent')->get();

            $tag_name = Tag::select('id', 'tag_name', 'color_code')->where("user_id", $this->user_id)->get();

            return view("agent.chats.list", compact("templates_data", "device", "tag_name", "agentData"));
        } catch (\Exception $e) {
            Log::error('Device query failed: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch device'], 500);
        }
    }

    public function markread_chats($uuid, $number)
    {
        if ($uuid == null) {
            return;
        }
        $device = Device::where("user_id", Auth::id())
            ->where("status", 1)
            ->where("uuid", $uuid)
            ->first();

        if (!empty($device)) {
            DB::table("messages")
                ->where("device_phone", $device->phone)
                ->where("sender_no", $number)
                ->update(["read" => 1]);
        }
        return response('success');
    }

    public function chats_history($uuid, $number)
    {
        if (!$uuid) return;

        $device = Device::where("uuid", $uuid)->where("status", 1)->firstOrFail();

        $agentDeviceRelation = AgentDeviceRelationModel::where('agent_id', Auth::id())
            ->where('device_id', $device->id)
            ->first();

        $chatType = $agentDeviceRelation->chat_type ?? 1;

        $contacts = [];

        if ($chatType == 2) {
            $accessContactIds = AgentContactRelationModel::where('agent_id', Auth::id())->pluck('contact_id');
            $contactsData = Contact::where('user_id', Auth::user()->parent_id)
                ->whereIn('id', $accessContactIds)
                ->pluck('phone');
            $contacts = $contactsData->toArray();
        } else {
            $contacts = [$number];
        }

        // Optimize: get both sent and received messages in one go
        $recieve_messages = DB::table("messages")
            ->select("id", "media", "body", "timestamp", "sender_no")
            ->where("device_phone", $device->phone)
            ->whereIn("sender_no", $contacts)
            ->get();

        $sent_messages = DB::table("task")
            ->select("task_id", "users.name as user_name", "users.avatar as user_avatar", "task_url", "created_at", "send_to_number", "templateId", "parameters", "text", "task_status", "task_description")
            ->join("users", "task.created_by", "=", "users.id")
            ->where("task.device_id", $device->id)
            ->whereIn("task.send_to_number", $contacts)
            ->get();

        // Collect all messages
        $all_messages = [];

        foreach ($recieve_messages as $rmessage) {
            $all_messages[] = [
                'primary_id' => $rmessage->id,
                'media' => $rmessage->media,
                'body' => $rmessage->body,
                'timestamp' => strtotime($rmessage->timestamp ?? 0),
                'message_type' => 'receive',
            ];
        }

        foreach ($sent_messages as $smessage) {
            $all_messages[] = [
                'primary_id' => $smessage->task_id,
                'user_name' => Auth::user()->name == $smessage->user_name ? __('You') : $smessage->user_name,
                'user_avatar' => $smessage->user_avatar,
                'media' => $smessage->task_url,
                'templateId' => $smessage->templateId,
                'parameters' => $smessage->parameters,
                'text' => $smessage->text,
                'timestamp' => strtotime($smessage->created_at ?? 0),
                'message_type' => 'sent',
                'task_status' => $smessage->task_status,
                'task_description' => $smessage->task_description
            ];
        }

        // Sort by timestamp descending (optional)
        usort($all_messages, fn($a, $b) => $b['timestamp'] <=> $a['timestamp']);

        // Get contact once
        // $contactData = Contact::whereIn('phone', $contacts)
        //     ->where('user_id', Auth::user()->parent_id)
        //     ->with('tag:id,tag_name,color_code')
        //     ->first();
        $contactData = Contact::where('phone', $number)
            ->where('user_id', Auth::user()->parent_id)
            ->with('tag:id,tag_name,color_code')
            ->first();

        $tag_info = $contactData?->tag ? [
            'tag_name' => $contactData->tag->tag_name,
            'color_code' => $contactData->tag->color_code
        ] : null;

        $blacklist = Blacklist::where('device_id', $device->id)->pluck('sender_no')->toArray();

        $blacklist_info = in_array($number, $blacklist);

        $contactId = $contactData?->id;

        $userNoteChat = null;
        $agentChatAssigned = null;

        if ($contactId) {
            $userNoteChat = UserNoteModel::select('users.name', 'user_note.created_at', 'user_note.note')
                ->join('users', 'user_note.user_id', '=', 'users.id')
                ->whereIn('user_note.user_id', [Auth::id(), Auth::user()->parent_id])
                ->where('user_note.device_id', $device->id)
                ->where('user_note.contact_id', $contactId)
                ->orderByDesc('user_note.created_at')
                ->get();

            $agentChatAssigned = AgentContactRelationModel::where('contact_id', $contactId)->get();
        }

        $templates = getFacebookTemplates($device->waid, $device->token);

        return response()->json([
            "chats" => $all_messages,
            "templates" => $templates,
            "tag_info" => $tag_info,
            "blacklist_info" => $blacklist_info,
            "user_chat_note" => $userNoteChat,
            "agent_chat_assign" => $agentChatAssigned,
        ]);
    }


    public function deleteChat($device_uuid, $number)
    {
        if ($device_uuid == null) {
            return;
        }
        $device = Device::where("user_id", Auth::id())
            ->where("status", 1)
            ->where("uuid", $device_uuid)
            ->first();

        if (!$device) {
            return response()->json(["message" => __("Device not found..!!"),], 401);
        }
        try {
            // delete media from messages table
            DB::table('messages')
                ->where('device_phone', $device->phone)
                ->where('sender_no', $number)
                ->whereNotNull('media') // Assuming media column stores file paths
                ->get()
                ->each(function ($message) {
                    // media file on public/uploads/incomming_media
                    $mediaPath = public_path('uploads/incomming_media/' . $message->media);
                    if (File::exists($mediaPath)) {
                        File::delete($mediaPath);
                    }
                });
            // delete chat for message table
            Messages::where('device_phone', $device->phone)
                ->where('sender_no', $number)
                ->delete();

            //delete chat for task table
            Task::where('device_id', $device->id)
                ->where('send_to_number', $number)
                ->delete();

            return response()->json(["success" => true, "message" => __("Chat deleted successfully..!!"),], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            // Log or handle the exception appropriately
            return response()->json(["error" => $e->getMessage()], 500);
        }
    }


    public function getGroupMetaData(Request $request)
    {
        if ($request->device_id == null) {
            return;
        }
        $device = Device::where("user_id", Auth::id())
            ->where("status", 1)
            ->where("uuid", $request->device_id)
            ->first();

        abort_if(empty($device), 404);

        $metaData = Cache::remember(
            "groups_" . $device->uuid . $request->id . "_" . session()->getId(),
            520,
            function () use ($device, $request) {
                return $this->groupMetaData($request->id, $device->id);
            }
        );

        return response()->json($metaData);
    }

    public function sendGroupBulkMessage(Request $request, $id)
    {
        if ($id == null) {
            return;
        }

        if (getUserPlanData("messages_limit") == false) {
            return response()->json(
                [
                    "message" => __("Maximum Monthly Messages Limit Exceeded"),
                ],
                401
            );
        }
        $device = Device::where("user_id", Auth::id())
            ->where("status", 1)
            ->where("uuid", $id)
            ->first();
        abort_if(empty($device), 404);

        if (count($request->groups) == 0) {
            return response()->json(
                [
                    "message" => __("Select Some Groups"),
                ],
                401
            );
        }

        $validated = $request->validate([
            "selecttype" => "required",
        ]);

        $success_requests = 0;
        $faild_requests = 0;
        $user = User::where("id", Auth::id())->first();

        if ($request->selecttype == "template") {
            $validated = $request->validate([
                "template" => "required",
            ]);
            $template = Template::where("user_id", Auth::id())
                ->where("status", 1)
                ->findorFail($request->template);

            foreach ($request->groups as $key => $group) {
                $isGroup = explode("@", $group);
                $isGroup = $isGroup[1];
                abort_if($isGroup != "g.us", 404);

                if (isset($template->body["text"])) {
                    $body = $template->body;


                    $text = $this->formatText(
                        $template->body["text"],
                        [],
                        $user
                    );
                    $body["text"] = $text;
                } else {
                    $body = $template->body;
                }
                $type = $template->type;

                try {
                    $response = $this->sendMessageToGroup(
                        $body,
                        $device->id,
                        $group,
                        $type,
                        true,
                        env('DELAY_TIME', 0)
                    );

                    if ($response["status"] == 200) {
                        $logs["user_id"] = Auth::id();
                        $logs["device_id"] = $device->id;
                        $logs["from"] = $device->phone ?? null;
                        $logs["to"] = "Group : " . $request->group_name;
                        $logs["template_id"] = $template->id ?? null;
                        $logs["type"] = "single-send";
                        $this->saveLog($logs);


                        $success_requests = $success_requests + 1;
                    } else {

                        $faild_requests = $faild_requests + 1;
                    }
                } catch (Exception $e) {
                    $faild_requests = $faild_requests + 1;
                }
            }
        } else {
            $validated = $request->validate([
                "message" => "required|max: 2000",
            ]);

            $text = $this->formatText($request->message);
            $body["text"] = $text;
            $type = "plain-text";

            foreach ($request->groups as $key => $group) {

                $isGroup = explode("@", $group);
                $isGroup = $isGroup[1];
                abort_if($isGroup != "g.us", 404);

                try {
                    $response = $this->sendMessageToGroup(
                        $body,
                        $device->id,
                        $group,
                        $type,
                        true,
                        env('DELAY_TIME', 0)
                    );

                    if ($response["status"] == 200) {
                        $logs["user_id"] = Auth::id();
                        $logs["device_id"] = $device->id;
                        $logs["from"] = $device->phone ?? null;
                        $logs["to"] = "Group : " . $request->group_name;
                        $logs["template_id"] = $template->id ?? null;
                        $logs["type"] = "single-send";
                        $this->saveLog($logs);

                        $success_requests = $success_requests + 1;
                    } else {
                        $faild_requests = $faild_requests + 1;
                    }
                } catch (Exception $e) {
                    $faild_requests = $faild_requests + 1;
                }
            }
        }

        return response()->json(
            [
                "message" => __("Total Message Sent in (" . $success_requests . ") Groups. Total Sending Faild in (" . $faild_requests . ") Groups"),
            ],
            200
        );
    }

    public function sendMessage(Request $request, $id)
    {
        // dd('hellow');
        // dd($request->all());
        if ($id == null) {
            // return;
            return response()->json(['message' => 'Device ID cannot be null'], status: 400);
        }
        if (getUserPlanData("messages_limit") == false) {
            return response()->json(
                [
                    "message" => __("Maximum Monthly Messages Limit Exceeded"),
                ],
                401
            );
        }

        $validated = $request->validate([
            "reciver" => "required|max:20",
            // "selecttype" => "required",
            "message" => "sometimes|required_without:mediafile|nullable:selecttype,template",
            "mediafile" => "sometimes|required_without:message",
            "voice_message" => "nullable|string",
            "templateselection" => "required_if:selecttype,template",
            // "mediafile" => "required_if:selecttype,template", // use mimes for extension |mimes:jpeg,png,pdf
        ]);

        // dd(Auth::id());
        $device = Device::where("status", 1)
            ->where("uuid", $id)
            ->first();
        // dd($device);

        $agentDeviceRelation = AgentDeviceRelationModel::where('agent_id', Auth::user()->id)->where('device_id', $device->id)->first();
        // dd($agentDeviceRelation);

        abort_if(empty($device), 404);

        $deviceid = $device->id;

        $device_id = $deviceid;
        $created_by = Auth::id();
        $scheduled_on = now();
        $send_to_number = $request->reciver;
        $templateId = $request->templateselection;
        $task_type = $request->tasktemp_type;
        $replyMsgId = $request->rep_msg_id ?? null;
        $parameters = is_array($request->variable) ? implode("||", $request->variable) : null;
        $ip = request()->ip();

        $task_url = null;
        if ($request->hasFile('mediafile')) {
            $media_file_data = $request->file('mediafile');
            $filename = uniqid() . '.' . $media_file_data->getClientOriginalExtension();
            $media_file_data->move(public_path('whatsappmedia'), $filename);
            $task_url = asset("whatsappmedia/{$filename}");
        }

        $tasktemp_url = null;
        if ($request->hasFile('mediatemplate_file')) {
            $tasktemp_data = $request->file('mediatemplate_file');
            $filename = uniqid() . '.' . $tasktemp_data->getClientOriginalExtension();
            $tasktemp_data->move(public_path('whatsappmedia'), $filename);
            $tasktemp_url = asset("whatsappmedia/{$filename}");
        }

        if ($request->voice_message) {
            $taskdata = [
                'device_id' => $device_id,
                'created_by' => $created_by,
                'scheduled_on' => $scheduled_on,
                'task_url' => $request->voice_message,
                'templateId' => "chat_reply",
                'is_reply' => 1,
                'task_type' => "1", // audio
                'parameters' => null,
                'text' => null,
                'ip' => $ip,
                'created_at' => now(),
                'updated_at' => now()
            ];
        }
        if ($request->selecttype === 'plain-text') {
            $reply_msg = $this->waba_json_generate($request->message ?? null, $task_url);
            $taskdata = [
                'device_id' => $device_id,
                'created_by' => $created_by,
                'scheduled_on' => $scheduled_on,
                'task_url' => $task_url,
                'templateId' => "chat_reply",
                'is_reply' => 1,
                'task_type' => "1",
                'parameters' => null,
                'text' => $reply_msg ?? null,
                'ip' => $ip,
                'created_at' => now(),
                'updated_at' => now()
            ];
        } elseif ($request->selecttype === 'template') {
            $taskdata = [
                'device_id' => $device_id,
                'created_by' => $created_by,
                'scheduled_on' => $scheduled_on,
                'task_url' => $tasktemp_url,
                'templateId' => $templateId,
                'task_type' => $task_type,
                'parameters' => $parameters,
                'text' => null,
                'ip' => $ip,
                'created_at' => now(),
                'updated_at' => now()
            ];
        }

        try {
            // dispatch(new BulkInsertTaskJob($taskdata, array($send_to_number)));
            // use high priority queue
            BulkInsertTaskJob::dispatch($taskdata, array($send_to_number))->onQueue('high');
            return response()->json(['message' => 'Message sent successfully'], 200);
        } catch (Exception $e) {
            return response()->json(['message' => 'Error sending the message: ' . $e->getMessage()], 500);
        }
    }

    public function chatHistory(Request $request, $id)
    {
        if ($id == null) {
            return;
        }
        // dd($this->user_id);
        // dd(Auth::user());
        $device = Device::where("status", 1)
            ->where("uuid", $id)
            ->first();
        // dd($device);
        abort_if(empty($device), 404);

        $days = $request->day;
        $msg_status = $request->status;
        $tag = $request->tag;
        $contsearch = $request->consearch;
        $last_timestamp = $request->lastTimesamp;

        $response = $this->getChats($device->phone, $device->id, $msg_status, $tag, $days, $contsearch, $last_timestamp);

        if ($response["status"] == 200) {
            // dd('hellow');
            $data["chats"] = $response["data"];
            $data["device_name"] = $device->name;
            $data["phone"] = $device->phone;
            return response()->json($data);
        }

        $data["message"] = $response["message"];
        $data["status"] = $response["status"];
        // dd($data);
        return response()->json($data, 401);
    }

    public function groups($id)
    {
        if ($id == null) {
            return;
        }
        $device = Device::where("user_id", Auth::id())
            ->where("status", 1)
            ->where("uuid", $id)
            ->first();
        abort_if(empty($device), 404);
        $templates = Template::where("user_id", Auth::id())
            ->where("status", 1)
            ->latest()
            ->get();
        return view("agent.chats.groups", compact("device", "templates"));
    }

    public function groupHistory($id)
    {
        if ($id == null) {
            return;
        }
        $device = Device::where("user_id", Auth::id())
            ->where("status", 1)
            ->where("uuid", $id)
            ->first();
        abort_if(empty($device), 404);

        $response = $this->getGroupList($device->id);

        if ($response["status"] == 200) {
            $data["chats"] = $response["data"];
            $data["device_name"] = $device->name;
            $data["phone"] = $device->phone;
            return response()->json($data);
        }

        $data["message"] = $response["message"];
        $data["status"] = $response["status"];

        return response()->json($data, 401);
    }

    public function sendGroupMessage(Request $request, $id)
    {
        if ($id == null) {
            return;
        }
        $device = Device::where("user_id", Auth::id())
            ->where("status", 1)
            ->where("uuid", $id)
            ->first();
        abort_if(empty($device), 404);

        $validated = $request->validate([
            "group" => "required|max:50",
            "group_name" => "required|max:100",
            "selecttype" => "required",
        ]);

        $isGroup = explode("@", $request->group);
        $isGroup = $isGroup[1];
        abort_if($isGroup != "g.us", 404);

        if ($request->selecttype == "template") {
            $validated = $request->validate([
                "template" => "required",
            ]);

            $template = Template::where("user_id", Auth::id())
                ->where("status", 1)
                ->findorFail($request->template);

            if (isset($template->body["text"])) {
                $body = $template->body;
                $user = User::where("id", Auth::id())->first();

                $text = $this->formatText($template->body["text"], [], $user);
                $body["text"] = $text;
            } else {
                $body = $template->body;
            }
            $type = $template->type;
        } else {
            $validated = $request->validate([
                "message" => "required|max: 500",
            ]);

            $text = $this->formatText($request->message);
            $body["text"] = $text;
            $type = "plain-text";
        }

        if (!isset($body)) {
            return response()->json(["error" => "Request Failed"], 401);
        }

        try {
            $response = $this->sendMessageToGroup(
                $body,
                $device->id,
                $request->group,
                $type,
                true,
                0
            );

            if ($response["status"] == 200) {
                $logs["user_id"] = Auth::id();
                $logs["device_id"] = $device->id;
                $logs["from"] = $device->phone ?? null;
                $logs["to"] = "Group : " . $request->group_name;
                $logs["template_id"] = $template->id ?? null;
                $logs["type"] = "single-send";
                $this->saveLog($logs);

                return response()->json(
                    [
                        "message" => __("Message sent successfully..!!"),
                    ],
                    200
                );
            } else {
                return response()->json(["error" => "Request Failed"], 401);
            }
        } catch (Exception $e) {
            return response()->json(["error" => "Request Failed"], 401);
        }
    }

    public function update_assign_chat(Request $request)
    {
        $validated = $request->validate([
            "agent_ids" => "required|array",
            "mobile_number" => "required",
        ]);

        $agentIds = $request->agent_ids;
        $mobile_number = $request->mobile_number;

        try {
            $contact = Contact::where('phone', $mobile_number)
                ->where('user_id', Auth::id())
                ->first();

            if (!$contact) {
                return response()->json(['error' => true, 'message' => 'Contact not found'], 404);
            }

            $contId = $contact->id;

            // 1. Get existing agent IDs for this contact
            $existingAgentIds = AgentContactRelationModel::where('contact_id', $contId)
                ->pluck('agent_id')
                ->toArray();

            // 2. Find agents to remove
            $agentsToRemove = array_diff($existingAgentIds, $agentIds);

            // 3. Remove those agent-contact relations
            if (!empty($agentsToRemove)) {
                AgentContactRelationModel::where('contact_id', $contId)
                    ->whereIn('agent_id', $agentsToRemove)
                    ->delete();
            }

            // 4. Add new agent-contact relations
            foreach ($agentIds as $agentId) {
                $agentExists = DB::table('users')->where('id', $agentId)->exists();

                if ($agentExists) {
                    $relationExists = AgentContactRelationModel::where('agent_id', $agentId)
                        ->where('contact_id', $contId)
                        ->exists();

                    if (!$relationExists) {
                        AgentContactRelationModel::create([
                            'agent_id' => $agentId,
                            'contact_id' => $contId,
                        ]);
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Assign chat to agents completed'], 200);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Assign chat to agents could not be completed',
                'exception' => $e->getMessage()
            ], 500);
        }
    }

    public function send_note_msg(Request $request)
    {
        $validated = $request->validate([
            "reply_msg" => "required",
            "mobile_number" => "required",
            "device_id" => 'required'
        ]);

        $reply_msg = $request->reply_msg;
        $mobile_number = $request->mobile_number;
        // dd($mobile_number);
        try {
            $contact = Contact::where('phone', $mobile_number)->where('user_id', Auth::user()->parent_id)->first();
            // dd($contact);
            // $contactData = Contact::where('phone', $number)->where('user_id', Auth::id())->first();
            // dd($contact);
            if (!$contact) {
                return response()->json(['error' => true, 'message' => 'Contact not found'], 404);
            }
            $contId = $contact->id;

            UserNoteModel::create([
                'user_id' => Auth::id(),
                'device_id' => $request->device_id,
                'contact_id' => $contId,
                'note' => $reply_msg
            ]);

            return response()->json(['success' => true, 'message' => 'Note added successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => true, 'message' => 'note not added'], 500);
        }
    }

    public function delete_chat_message(Request $request)
    {
        $validated = $request->validate([
            "primary_id" => "required",
            "type" => "required"
        ]);

        $primaryId = $request->primary_id;
        $type = $request->type;
        try {
            if ($type == 'receive') {

                $message = Messages::where('id', $primaryId)
                    ->first();

                if ($message->media) {
                    $mediaPath = public_path('uploads/incomming_media/' . $message->media);
                    if (File::exists($mediaPath)) {
                        File::delete($mediaPath);
                    }
                }
                $message->delete();
            } elseif ($type == 'send') {
                $task = Task::where('task_id', $primaryId)->first();

                if ($task->task_url) {
                    $relativePath = str_replace(url('/'), '', $task->task_url);
                    $filePath = public_path($relativePath);

                    if (File::exists($filePath)) {
                        File::delete($filePath);
                    }
                }

                $task->delete();
            }
            return response()->json(['success' => true, 'message' => 'Chat delete successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => true, 'message' => 'chat not deleted'], 500);
        }
    }

    public function blockUnblockUser(Request $request)
    {
        $device = Device::where('uuid', $request->uuid)->first();

        if (isset($request->status) && $request->status == 'unblock') {

            $blackList = Blacklist::where('device_id', $device->id)->where('sender_no', $request->number)->first();
            $blackList->delete();

            return response()->json(['success' => true, 'message' => 'Number unblocked successfully.'], 200);
        } else {

            $blackList = new Blacklist();
            $blackList->device_id = $device->id;
            $blackList->sender_no = $request->number;
            $blackList->save();

            return response()->json(['success' => true, 'message' => 'Number blocked successfully.'], 200);
        }
    }
}
