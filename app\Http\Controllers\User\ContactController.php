<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\TutorialVideo;
use Exception;
use Illuminate\Http\Request;
use App\Models\Contact;
use App\Models\Template;
use App\Models\Device;
use App\Models\User;
use App\Rules\Phone;
use App\Traits\Whatsapp;
use App\Models\Group;
use App\Models\Groupcontact;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Response;
use DB;
//ImportContactsJob
use App\Jobs\ContactImport;
use App\Models\Blacklist;
use Carbon\Carbon;

class ContactController extends Controller
{
    use Whatsapp;



    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $authid = Auth::id();
        $query = Contact::where('user_id', $authid)->with(['groupcontact', 'tag'])->latest();

        if (!empty($request->search)) {
            $searchType = $request->type;
            if ($searchType === 'name') {
                // Make the search case-insensitive using lower()
                $query->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($request->search) . '%']);
            } elseif ($searchType === 'phone') {
                // Make the phone number search case-insensitive
                $query->whereRaw('LOWER(phone) LIKE ?', ['%' . strtolower($request->search) . '%']);
            } elseif ($searchType === 'tag_name') {
                // Case-insensitive search on the related tag name using whereHas and lower()
                $query->whereHas('tag', function ($query) use ($request) {
                    $query->whereRaw('LOWER(tag_name) LIKE ?', ['%' . strtolower($request->search) . '%']);
                });
            }
        }
        $type = $request->type ?? '';

        $contacts = $query->paginate(10);

        $total_contacts = Contact::where('user_id', $authid)->count();
        // $contacts = Contact::where('user_id', Auth::id())->with('groupcontact')->latest()->paginate(20);
        $devices = Device::where('user_id', $authid)->where('status', 1)->latest()->get();
        $limit = json_decode(Auth::user()->plan);
        $limit = $limit->contact_limit ?? 0;

        if ($limit == '-1') {
            $limit = number_format($total_contacts);
        } else {
            $limit = $total_contacts . ' / ' . $limit;
        }

        $groups = Group::where('user_id', $authid)->latest()->get();

        $videoTutorial = TutorialVideo::where('key', 'contact_book')->first();

        return view('user.contact.index', compact('contacts', 'type', 'total_contacts', 'devices', 'limit', 'groups', 'videoTutorial'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $groups = Group::where('user_id', Auth::id())->latest()->get();
        return view('user.contact.create', compact('groups'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (getUserPlanData('contact_limit') == false) {
            return response()->json([
                'message' => __('Maximum Contacts Limit Exceeded')
            ], 401);
        }


        $validated = $request->validate([
            'phone' => ['required', new Phone],
            'name' => ['required', 'max:20'],
        ]);

        $is_exist = Contact::where('user_id', Auth::id())->where('phone', $request->phone)->first();
        if (!empty($is_exist)) {
            return response()->json([
                'message' => __('Contact already exist..!!'),
                'redirect' => route('user.contact.index')
            ], 401);
        }

        if ($request->group) {
            $group = Group::where('user_id', Auth::id())->findorFail($request->group);
        }

        $contact = new Contact;
        $contact->name = $request->name;
        $contact->phone = $request->phone;
        $contact->user_id = Auth::id();
        $contact->anniversary_date = $request->anniversary_date ?? null;
        $contact->birthday_date = $request->birthday_date ?? null;
        $contact->save();

        if ($request->group) {
            $contact->groupcontacts()->insert(['group_id' => $request->group, 'contact_id' => $contact->id]);
        }

        return response()->json([
            'message' => __('New Contact Created Successfully'),
        ], 200);
    }



    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $info = Contact::where('user_id', Auth::id())->findorFail($id);
        return view('user.contact.edit', compact('info'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'phone' => ['required', new Phone],
            'name' => ['required', 'max:20'],
        ]);

        $is_exist = Contact::where('user_id', Auth::id())->where('phone', $request->phone)->where('id', '!=', $id)->first();
        if (!empty($is_exist)) {
            return response()->json([
                'message' => __('Opps this contact number you have already added')
            ], 401);
        }

        $contact = Contact::where('user_id', Auth::id())->findorFail($id);
        $contact->name = $request->name;
        $contact->phone = $request->phone;
        $contact->birthday_date = $request->birthday ?? null;
        $contact->anniversary_date = $request->anniversary ?? null;
        $contact->user_id = Auth::id();
        $contact->save();

        if ($request->group) {
            $group = Group::where('user_id', Auth::id())->findorFail($request->group);
            $contact->groupcontact()->sync([$request->group]);
        }


        return response()->json([
            'message' => __('Contact update Successfully'),
            'redirect' => route('user.contact.index')
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $contact = Contact::where('user_id', Auth::id())->findorFail($id);
        $contact->delete();

        return response()->json([
            'message' => __('Contact deleted successfully..!!'),
            'redirect' => route('user.contact.index')
        ], 200);
    }

    public function sendtemplateBulk(Request $request)
    {

        if (getUserPlanData('messages_limit') == false) {
            return response()->json([
                'message' => __('Maximum Monthly Messages Limit Exceeded')
            ], 401);
        }

        $validated = $request->validate([
            'template' => ['required'],
            'group' => ['required'],
        ]);

        $group = Group::where('user_id', Auth::id())->whereHas('groupcontacts')->findorFail($request->group);
        $device = Device::where('user_id', Auth::id())->where('status', 1)->findorFail($request->device);

        return response()->json([
            'message' => __('Redirecting to bulk sending page'),
            'redirect' => url('user/sent-bulk-with-template/' . $request->template . '/' . $group->id . '/' . $device->uuid)
        ], 200);


        // $validated = $request->validate([
        //  'group'   => ['required'],
        //  'template'   => ['required'],
        // ]);

        // $template=Template::where('user_id',Auth::id())->where('status',1)->findorFail($request->template);
        // $device=Device::where('user_id',Auth::id())->where('status',1)->findorFail($request->device);
        // $contacts=Contact::where('user_id',Auth::id())->whereHas('groupcontacts',function($q) use ($request){
        //     return $q->where('group_id',$request->group);
        // })->get();

        // $user=User::where('id',Auth::id())->first();

        // $logs['template_id']=$template->id;

        // foreach ($contacts as $key => $contact) {
        //   if (isset($template->body['text'])) {

        //     $formatText=$this->formatText($template->body['text'],$contact,$user);
        //     $body=$template->body;
        //     $body['text']=$formatText;
        //     $logs['template_id']=$template->id;

        //   }
        //   else{
        //     $body=$template->body;
        //   }



        //   $response = $this->messageSend($body,$device->id,$contact->phone,$template->type,true);

        //   if ($response['status'] == 200) {
        //      $logs['user_id']=$user->id;
        //      $logs['device_id']=$device->id;
        //      $logs['from']=$device->phone ?? null;
        //      $logs['to']=$contact->phone;
        //      $logs['type']='bulk-message';

        //      $this->saveLog($logs);
        //   }
        // }

        // return response()->json([
        //     'message'  => __('Message Send Successfully'),
        // ], 200);

    }


    public function import(Request $request)
    {
        $validated = $request->validate([
            'file' => 'required|mimes:xlsx,xls|max:25000',
        ]);

        if (getUserPlanData('contact_limit') == false) {
            return response()->json([
                'message' => __('Maximum Contacts Limit Exceeded')
            ], 401);
        } else {
            $contact_limit = json_decode(Auth::user()->plan);
            $contact_limit = $contact_limit->contact_limit;
        }


        if ($request->group) {
            $group = Group::where('user_id', Auth::id())->findorFail($request->group);
        }


        $file = $request->file('file');

        $insertable = [];

        // Use PhpSpreadsheet to read Excel files
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file);
        $spreadsheet = $reader->load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();

        // First row is the header
        $header = array_shift($rows);

        // Process the remaining rows
        foreach ($rows as $data) {
            if (empty($data[1])) {
                continue; // Skip if no phone number
            }

            $row = array(
                'name' => $data[0] ?? null,
                'phone' => $data[1],
                'birthday_date' => $data[2] ?? null,
                'anniversary_date' => $data[3] ?? null,
            );
            array_push($insertable, $row);
        }

        $count_contacts = count($insertable);

        if ($contact_limit != -1) {
            $old_rows = Contact::where('user_id', Auth::id())->count();

            $available_rows = $contact_limit - $old_rows;



            if ($count_contacts > $available_rows) {
                return response()->json([
                    'message' => __('Maximum ' . $available_rows . ' records are available only for create contact')
                ], 401);
            }
        }


        try {

            $group_id = $request->group ?? null;
            $user_id = Auth::id();
            $chunks = array_chunk($insertable, 1000); // Chunk the insertable array into chunks of 1000
            $delay = 0;

            foreach ($chunks as $chunk) {
                dispatch(new ContactImport($chunk, $user_id, $group_id))->delay(now()->addSeconds($delay));
                $delay += 15;
            }
        } catch (Exception $th) {


            return response()->json([
                'message' => $th->getMessage()
            ], 500);
        }


        //set session for flash message

        return response()->json([
            'status' => 'success',
            'message' => __('Contact list imported successfully'),
            'redirect' => route('user.contact.index'),
        ], 200);
    }

    public function exportContacts()
    {
        $filename = 'contacts.xls';
        $headers = [
            'Content-Type' => 'application/vnd.ms-excel',
            'Content-Disposition' => "attachment; filename=$filename",
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $contacts = Contact::where('user_id', Auth::id())
            ->with('groupcontact:id,name')
            ->get();

        $table = '<table>';
        $table .= '<tr><th>Whatsapp Number</th><th>Name</th><th>Group</th><th>Birthday</th><th>Anniversary</th></tr>';

        foreach ($contacts as $contact) {
            $table .= '<tr>';
            $table .= '<td>' . $contact['phone'] . '</td>';
            $table .= '<td>' . ($contact['name'] ?? '') . '</td>';
            $table .= '<td>' . ($contact['groupcontact'][0]['name'] ?? '') . '</td>';
            $table .= '<td>' . ($contact['birthday_date'] ?? '') . '</td>';
            $table .= '<td>' . ($contact['anniversary_date'] ?? '') . '</td>';
            $table .= '</tr>';
        }

        $table .= '</table>';

        return response($table, 200, $headers);
    }

    public function blacklistContacts(Request $request)
    {
        $subQuery = Blacklist::select('blacklists.id', 'blacklists.sender_no', 'blacklists.created_at', 'devices.name', 'devices.phone')
            ->join('devices', 'blacklists.device_id', '=', 'devices.id')
            ->where('devices.user_id', Auth::id());
        $query = $subQuery->latest();
        // dd($query);
        //$query = Contact::where('user_id', Auth::id())->with('groupcontact')->latest();

        if (!empty($request->search)) {
            $searchType = $request->type;
            if ($searchType === 'name') {
                $query->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($request->search) . '%']);
            } elseif ($searchType === 'phone') {
                $query->whereRaw('LOWER(phone) LIKE ?', ['%' . strtolower($request->search) . '%']);
            } elseif ($searchType === 'blacklist') {
                $query->whereRaw('LOWER(sender_no) LIKE ?', ['%' . strtolower($request->search) . '%']);
            }
        }
        $type = $request->type ?? '';

        //$contacts = $query->paginate(10);
        //$total_contacts = $subQuery->count();

        $groups = Group::where('user_id', Auth::id())->latest()->get();

        // $devices = Device::where('user_id', Auth::id())->where('status', 1)->latest()->get();
        $devices = Device::where('user_id', Auth::id())->where('status', 1)->get();

        if ($devices->isEmpty()) {
            return response()->json(['message' => 'No active device found.'], 404);
        }

        $allContacts = [];

        foreach ($devices as $device) {
            // dd($device->id);
            $blacklist = Blacklist::where('device_id', $device->id)->get();
            // dd($blacklist);
            foreach ($blacklist as $black) {
                $deviceData = Device::where('id', $black->device_id)->first();
                $allContacts[] = [
                    'number' => $black->sender_no,
                    'name' => $deviceData->name,
                    'phone' => $deviceData->phone,
                    'uuid' => $deviceData->uuid,
                ];
            }
        }

        $total_contacts = count($allContacts);

        return view('user.contact.blacklists', compact('allContacts', 'type', 'total_contacts',  'groups'));
    }
}
