<?php

namespace App\Http\Controllers;

use App\Jobs\SendTaskJob;
use App\Models\AgentContactRelationModel;
use App\Models\Blacklist;
use Illuminate\Http\Request;
use App\Traits\Whatsapp;
use App\Traits\Telegram;
use App\Traits\Bothandler;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Models\Reply;
use App\Models\Device;
use App\Models\Contact;
use App\Models\DripCampaign;
use App\Models\Tag;
use App\Models\Task;
use App\Models\User;
use App\Models\Order;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;
//composer require phpseclib/phpseclib:~3.0
use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Crypt\RSA;
use phpseclib3\Crypt\AES;
use Illuminate\Support\Str;
use GGInnovative\Larafirebase\Facades\Larafirebase;
use Illuminate\Support\Facades\Cache;

class WebHookController extends Controller
{
    use Whatsapp;
    use Telegram;
    use Bothandler;
    public function webhookHandler(Request $request)
    {
        $input = json_decode(file_get_contents('php://input'), true);
        Log::debug('Input Webhook Response: ' . json_encode($input));

        // Handle failed message events
        // {"error":null,"inValidNumbers":null,"inValidNumbers_Count":0,"messageId":"36b42d96-429c-4e20-88fb-b3053935f297","reachableUsers":["+919510991141"],"reachableUsers_Count":1,"referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","repeatedNumbers":[],"repeatedNumbers_Count":0,"status":"200","total limit":0,"unReachableUsers":null,"unReachableUsers_Count":0}
        if (isset($input['error']) || !empty($input['unReachableUsers'])) {
            // Log::debug('Failed message event detected');

            Log::info('Failed message event detected with', [
                'referenceID' => $input['referenceID'] ?? null,
                'unReachableUsers' => $input['unReachableUsers'] ?? [],
                'unReachableUsers_Count' => $input['unReachableUsers_Count'] ?? 0,
            ]);

            $messageId = $input['messageId'] ?? null;
            $referenceID = $input['referenceID'] ?? null;
            $unReachableUsers = $input['unReachableUsers'] ?? [];
            $error = $input['error'] ?? null;
            $unReachableUsers_Count = $input['unReachableUsers_Count'] ?? 0;

            // dd($unReachableUsers);
            // Handle unreachable users
            if (!empty($unReachableUsers) && $referenceID) {
                // dd('unReachableUsers',$unReachableUsers);
                // dd('referenceID',$referenceID);
                foreach ($unReachableUsers as $number) {
                    // Normalize phone number
                    // $normalizedNumber = ltrim($number, '+');
                    $normalizedNumber = $this->normalizePhoneNumber($number);

                    // Find and update task as failed
                    $task = Task::where('whatsapp_id', $referenceID)->where('send_to_number', $normalizedNumber)->first();
                    // ->where('send_to_number', $normalizedNumber)
                    // dd('all tasks',$tasks);
                    // dd('tasks', $tasks);
                    // foreach ($tasks as $task) {
                    // dd('task',$task);
                    // dd('normalizedNumber', $normalizedNumber);
                    // dd('task->send_to_number', $task->send_to_number);
                    // dd('task->whatsapp_id', $task->whatsapp_id);

                    // $referenceID === $task->whatsapp_id && $normalizedPhone && $task->send_to_number == $normalizedPhone
                    if ($referenceID === $task->whatsapp_id && $normalizedNumber && $task->send_to_number == $normalizedNumber) {
                        // dd('task', $task);
                        // Update task status as failed
                        $this->msg_status_update($referenceID, $normalizedNumber, time(), 4, 'Unreachable');

                        // Refund credit to user when message is unreachable
                        $this->refundCreditForFailedMessage($task, 'Unreachable');

                        Log::debug("Task updated as failed and credit refunded for unreachable number: {$normalizedNumber}");
                    }
                    // dd('vishal');
                    // if ($task) {
                    // Update task status as failed
                    // $this->msg_status_update($referenceID, time(), 4, 'Unreachable');

                    // Refund credit to user when message is unreachable
                    // $this->refundCreditForFailedMessage($task, 'Unreachable');

                    // Log::debug("Task updated as failed and credit refunded for unreachable number: {$normalizedNumber}");
                    // }
                }
            }
            // dd('vishal');
            // Handle general errors
            // if ($error && $referenceID) {
            //     $tasks = Task::where('whatsapp_id', $referenceID)->get();
            //     foreach ($tasks as $task) {
            //         // Update task status as failed
            //         $this->msg_status_update($referenceID, time(), 4, 'Error: ' . $error);

            //         // Refund credit to user when message fails due to error
            //         $this->refundCreditForFailedMessage($task, 'Error: ' . $error);

            //         Log::debug("Task updated as failed and credit refunded due to error: {$error}");
            //     }
            // }
        }

        // sent message event is
        // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"eventId":"e3f76d15-fb8f-4460-bdc4-e6a180b8f1cd","eventType":"SEND_MESSAGE_SUCCESS","messageId":"36b42d96-429c-4e20-88fb-b3053935f297","referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","sendTime":"2025-06-03T18:07:21.601332+05:30","senderPhoneNumber":"+919510991141"},"entityType":"STATUS_EVENT","userPhoneNumber":"+919510991141"}

        // Handle message events (both status and user messages)
        if (isset($input['entity']['messageId']) || isset($input['entity'])) {
            Log::debug('Processing message event');

            $agentId = $input['agentId'] ?? null;
            $eventId = $input['entity']['eventId'] ?? null;
            $eventType = $input['entity']['eventType'] ?? null;
            $messageId = $input['entity']['messageId'] ?? null;
            $referenceID = $input['entity']['referenceID'] ?? null;
            $sendTime = $input['entity']['sendTime'] ?? null;
            $senderPhoneNumber = $input['entity']['senderPhoneNumber'] ?? null;
            $entityType = $input['entityType'] ?? null;
            $userPhoneNumber = $input['userPhoneNumber'] ?? null;

            $device = null;
            if ($agentId) {
                $device = Device::where('token', $agentId)->first();
            }

            // $normalizedPhone = $senderPhoneNumber ? ltrim($senderPhoneNumber, '+') : null;
            $normalizedPhone = $this->normalizePhoneNumber($senderPhoneNumber);


            if ($userPhoneNumber) {
                // $normalizedUserPhone = ltrim($userPhoneNumber, '+');
                $normalizedUserPhone = $this->normalizePhoneNumber($userPhoneNumber);
            }

            // Convert sendTime to timestamp for database operations
            $timestamp = null;
            if ($sendTime) {
                try {
                    $timestamp = strtotime($sendTime);
                } catch (Exception $e) {
                    Log::error('Error parsing sendTime: ' . $e->getMessage());
                    $timestamp = time();
                }
            }

            // Handle outgoing message status events
            if ($entityType == 'STATUS_EVENT' && $eventType == 'SEND_MESSAGE_SUCCESS' && $messageId) {
                Log::debug('Processing SEND_MESSAGE_SUCCESS event');
                $tasks = Task::where('whatsapp_id', $referenceID)->get();
                foreach ($tasks as $task) {
                    if ($referenceID === $task->whatsapp_id && $normalizedPhone && $task->send_to_number == $normalizedPhone) {
                        $this->msg_status_update($referenceID, $normalizedPhone, $timestamp, 1);
                        Log::debug("Task status updated to sent for messageId: {$referenceID}");
                    }
                }
            }
            //delivered message event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"eventId":"MxPcZIHRPgSWKXooN-afGObg","eventType":"MESSAGE_DELIVERED","messageId":"36b42d96-429c-4e20-88fb-b3053935f297","referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","sendTime":"2025-06-03T12:37:23.098771Z","senderPhoneNumber":"+919510991141"},"entityType":"USER_EVENT","userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_EVENT' && $eventType == 'MESSAGE_DELIVERED' && $messageId) {
                Log::debug('Processing MESSAGE_DELIVERED event');
                $tasks = Task::where('whatsapp_id', $referenceID)->get();
                foreach ($tasks as $task) {
                    if ($referenceID === $task->whatsapp_id && $normalizedPhone && $task->send_to_number == $normalizedPhone) {
                        $this->msg_status_update($referenceID, $normalizedPhone, $timestamp, 2);
                        Log::debug("Task status updated to delivered for messageId: {$referenceID}");
                    }
                }
            }
            //read message event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"eventId":"MxJNylvJhISg2HxQSOD7uztQ","eventType":"MESSAGE_READ","messageId":"36b42d96-429c-4e20-88fb-b3053935f297","referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","sendTime":"2025-06-03T12:37:23.099566Z","senderPhoneNumber":"+919510991141"},"entityType":"USER_EVENT","userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_EVENT' && $eventType == 'MESSAGE_READ' && $messageId) {
                Log::debug('Processing MESSAGE_READ event');
                $tasks = Task::where('whatsapp_id', $referenceID)->get();
                foreach ($tasks as $task) {
                    if ($referenceID === $task->whatsapp_id && $normalizedPhone && $task->send_to_number == $normalizedPhone) {
                        $this->msg_status_update($referenceID, $normalizedPhone, $timestamp, 3);
                        Log::debug("Task status updated to read for messageId: {$referenceID}");
                    }
                }
            }

            // user clicked on button event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"location":null,"messageId":"36b42d96-429c-4e20-88fb-b3053935f297","referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","sendTime":"2025-06-03T12:38:58.850883Z","suggestionResponse":{"plainText":"Auto Reply","postBack":{"data":"reply_suggestion_1"},"type":"REPLY"},"text":null,"userFile":null},"entityType":"USER_MESSAGE","metaData":{"orgMsgId":"7e94796c-99b2-4f74-ab85-eefebbf2a935","orgMsgSendTime":"2025-06-03T12:37:21.600961Z"},"userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_MESSAGE' && !empty($input['entity']['suggestionResponse']) && $device) {
                Log::debug('Processing button click event');
                $plainText = $input['entity']['suggestionResponse']['plainText'] ?? '';
                $postBackData = $input['entity']['suggestionResponse']['postBack']['data'] ?? '';

                // dd('$device',$device);
                // Insert message into database
                $this->message($device->phone ?? '918320677031', $normalizedUserPhone, $timestamp, 'button_reply', $plainText, null, $agentId);
                Log::debug("Button click message inserted: {$plainText}");
            }

            //normal text message event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"location":null,"messageId":"MxZyBxP2zzSPm2wpnY3hpCHg","sendTime":"2025-06-03T13:01:10.722264Z","suggestionResponse":null,"text":"Reoly test","userFile":null},"entityType":"USER_MESSAGE","userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_MESSAGE' && array_key_exists('text', $input['entity']) && $input['entity']['text'] !== null && $device) {
                Log::debug('Processing text message event');
                $text = $input['entity']['text'];

                // Insert message into database
                $this->message($device->phone ?? '918320677031', $normalizedUserPhone, $timestamp, 'text', $text, null, $agentId);
                Log::debug("Text message inserted: {$text}");
            }

            // media message event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"location":null,"messageId":"MxWX=cNwzfTtePFiCtRzVpQg","sendTime":"2025-06-03T13:04:58.699986Z","suggestionResponse":null,"text":null,"userFile":{"category":null,"payload":{"fileName":null,"fileSizeBytes":225459,"fileUri":"https:\/\/rcs-copper-ap.googleapis.com\/blob\/65331166-429a-4bbf-9c86-d8763643362c\/a78d7209ffd1ea3380be62536f36cf919dbd561c7e65038a31e68773024b","mimeType":"image\/jpeg"},"thumbnail":{"fileName":null,"fileSizeBytes":12056,"fileUri":"https:\/\/rcs-copper-ap.googleapis.com\/blob\/65331166-429a-4bbf-9c86-d8763643362c\/f73c7f71a0a7625ba5a9db54daa9486be3c095d6f20b1d22d0a27e2dedb5","mimeType":"image\/jpeg"}}},"entityType":"USER_MESSAGE","userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_MESSAGE' && isset($input['entity']['userFile']) && !empty($input['entity']['userFile']['payload']['fileUri']) && $device) {
                Log::debug('Processing media message event');
                $userFile = $input['entity']['userFile'];
                $fileUri = $userFile['payload']['fileUri'] ?? '';
                $mimeType = $userFile['payload']['mimeType'] ?? '';

                // Determine media type based on mimeType
                $mediaType = 'media';
                if (strpos($mimeType, 'image/') === 0) {
                    $mediaType = 'image';
                } elseif (strpos($mimeType, 'video/') === 0) {
                    $mediaType = 'video';
                } elseif (strpos($mimeType, 'audio/') === 0) {
                    $mediaType = 'audio';
                } elseif (strpos($mimeType, 'application/') === 0) {
                    $mediaType = 'document';
                }

                // Insert message into database
                $this->message($device->phone ?? '918320677031', $normalizedUserPhone, $timestamp, $mediaType, null, $fileUri, $agentId);
                Log::debug("Media message inserted: {$mediaType} - {$fileUri}");
            }
        }

        return response($request);
    }

    function normalizePhoneNumber($phoneNumber)
    {
        // Log::info('normalizePhoneNumber called for: ' . $phoneNumber);
        // Remove invisible Unicode characters (Left-to-Right Embedding, Pop Directional Formatting, etc.)
        $normalized = preg_replace('/[\x{200E}\x{200F}\x{202A}-\x{202E}]/u', '', $phoneNumber);

        // Remove + sign
        $normalized = ltrim($normalized, '+');

        // Remove any remaining whitespace
        $normalized = trim($normalized);

        Log::info('Function Normalized phone number: ' . $normalized);
        return $normalized;
    }

    public function bothookHandler(Request $request, $device_phone)
    {
        $input = json_decode(file_get_contents('php://input'), true);
        // Log::debug($input);
        $device = Device::with('user')->where('phone', $device_phone)->first();

        $sent_to_number = $input['send_to_number'];
        $text = $input['text'];
        //insert into task table
        $ip = request()->ip();
        $launch_time = now();
        $uuid = (string) Str::uuid();

        $taskdata = [
            'device_id' => $device->id,
            'created_by' => $device->user_id,
            'scheduled_on' => $launch_time,
            'whatsapp_sent_time' => $launch_time,
            'task_url' => null,
            'task_status' => 2,
            'campaign_name' => null,
            'templateId' => "auto_reply",
            'is_reply' => 1,
            'task_type' => "1",
            'parameters' => null,
            'send_to_number' => $sent_to_number,
            'text' => $text,
            'ip' => $ip,
            'whatsapp_id' => $uuid,
            'created_at' => now(),
            'updated_at' => now()
        ];

        try {
            DB::table('task')->insert($taskdata);
        } catch (\Exception $e) {
            return response($e->getMessage());
        }

        //retun response 200
        return response('inserted', 200);
    }

    function decryptRequest($body, $privatePemLocation)
    {
        $encryptedAesKey = base64_decode($body['encrypted_aes_key']);
        $encryptedFlowData = base64_decode($body['encrypted_flow_data']);
        $initialVector = base64_decode($body['initial_vector']);
        $privatePem = file_get_contents('file://' . $privatePemLocation);
        // Decrypt the AES key created by the client
        $rsa = PublicKeyLoader::load($privatePem, 'nxc@123')
            ->withPadding(RSA::ENCRYPTION_OAEP)
            ->withHash('sha256')
            ->withMGFHash('sha256');

        $decryptedAesKey = $rsa->decrypt($encryptedAesKey);
        if (!$decryptedAesKey) {
            throw new Exception('Decryption of AES key failed.');
        }

        // Decrypt the Flow data
        $aes = new AES('gcm');
        $aes->setKey($decryptedAesKey);
        $aes->setNonce($initialVector);
        $tagLength = 16;
        $encryptedFlowDataBody = substr($encryptedFlowData, 0, -$tagLength);
        $encryptedFlowDataTag = substr($encryptedFlowData, -$tagLength);
        $aes->setTag($encryptedFlowDataTag);

        $decrypted = $aes->decrypt($encryptedFlowDataBody);
        if (!$decrypted) {
            throw new Exception('Decryption of flow data failed.');
        }

        return [
            'decryptedBody' => json_decode($decrypted, true),
            'aesKeyBuffer' => $decryptedAesKey,
            'initialVectorBuffer' => $initialVector,
        ];
    }

    function encryptResponse($response, $aesKeyBuffer, $initialVectorBuffer)
    {
        // Flip the initialization vector
        $flipped_iv = ~$initialVectorBuffer;

        // Encrypt the response data
        $cipher = openssl_encrypt(json_encode($response), 'aes-128-gcm', $aesKeyBuffer, OPENSSL_RAW_DATA, $flipped_iv, $tag);
        return base64_encode($cipher . $tag);
    }

    public function subscribe(Request $request)
    {
        return response($request->get('hub_challenge'), 200);
    }

    public function msg_status_update($whatsapp_id, $sent_to_number, $statuses_timestamp, $task_status, $fail_error = null)
    {
        //$final status 1 = whatsapp_sent_time , 2 = whatsapp_received_time , 3 whatsapp_read_time , 4 =no time
        //0-pending,1-sent,2-delievered,3-read,4-failed
        switch ($task_status) {
            case '1':
                $updateData = [
                    'whatsapp_sent_time' => date("Y-m-d H:i:s", $statuses_timestamp),
                    'task_status' => $task_status,
                ];
                break;
            case '2':
                $updateData = [
                    'whatsapp_received_time' => date("Y-m-d H:i:s", $statuses_timestamp),
                    'task_status' => $task_status,
                ];
                break;
            case '3':
                $updateData = [
                    'whatsapp_read_time' => date("Y-m-d H:i:s", $statuses_timestamp),
                    'task_status' => $task_status,
                ];
                break;
            case '4':
                $updateData = [

                    'task_status' => $task_status,
                    'whatsapp_sent_time' => date("Y-m-d H:i:s", $statuses_timestamp),
                    'task_description' => $fail_error ?? '',
                ];
                break;
            default:
                return;
        }

        // $query = DB::table('task')
        //     ->where('whatsapp_id', $whatsapp_id)
        //     ->update($data);
        Task::where('whatsapp_id', $whatsapp_id)
            ->where('send_to_number', $sent_to_number)
            ->update($updateData);
    }

    public function sendAutoMessage($statuses_id)
    {
        $task = Task::where('whatsapp_id', $statuses_id)->first();
        if (!$task) {
            return;
        }
        //Log::info('taskdata for auto send message: ' . $task);

        $uuid = (string) Str::uuid();

        $scheduleOn = Carbon::now()->addDay();

        $taskdata = [
            'device_id' => $task->device_id,
            'created_by' => $task->created_by,
            'scheduled_on' => $scheduleOn,
            'send_to_number' => $task->send_to_number,
            'campaign_name' => $task->campaign_name,
            'templateId' => $task->templateId,
            'text' => $task->text,
            'parameters' => $task->parameters,
            'task_url' => $task->task_url,
            'task_type' => $task->task_type,
            'is_reply' => $task->is_reply,
            'whatsapp_id' => $uuid,
            'ip' => $task->ip,
            'created_at' => now(),
            'updated_at' => now()
        ];
        //dd($taskdata);
        try {
            DB::table('task')->insert($taskdata);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error Save Data : ' . $e->getMessage());
        }

        try {
            //use high priority queue
            // SendTaskJob::dispatch($waid)->onQueue('high');
            SendTaskJob::dispatch($uuid)->onQueue('high')->delay(now()->setTimeFromTimeString($scheduleOn));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
        }
    }

    public function message($device_phone, $from, $time_stamp, $type, $message_caption, $file, $agentId)
    {
        //get usr id from user master
        //Log::info('Type for message function: ' . $type);

        $device = Device::with('user')->where('token', $agentId)->first();
        // dd($device);
        // Handle timestamp conversion properly
        $timestamp = null;
        if ($time_stamp) {
            if (is_numeric($time_stamp)) {
                // Unix timestamp
                $timestamp = date("Y-m-d H:i:s", $time_stamp);
            } else {
                // String timestamp, try to parse it
                try {
                    $timestamp = date("Y-m-d H:i:s", strtotime($time_stamp));
                } catch (\Exception $e) {
                    Log::error('Error parsing timestamp: ' . $e->getMessage());
                    $timestamp = now();
                }
            }
        } else {
            $timestamp = now();
        }

        $data = [
            'device_phone' => $device_phone ?? '918320677031',
            'sender_no' => $from,
            'timestamp' => $timestamp,
            'type' => $type,
            'body' => $message_caption ?? '',
            'media' => $file ?? null,
            'created_at' => now(),
            'updated_at' => now()
        ];

        try {
            DB::table('messages')->insert($data);
            Log::debug("Message inserted successfully for device: {$device_phone}, type: {$type}");
        } catch (\Exception $e) {
            Log::error('Error inserting message data: ' . $e->getMessage());
        }

        if ($type == 'text' || $type == 'button' || $type == 'list_reply' || $type == 'request_welcome') {
            $driver = DB::connection()->getDriverName();

            // $replies = Reply::where('device_id', $device->id)->with('template')->where('status',1)->where('keyword', 'LIKE', '%' . $message_caption . '%')->latest()->get();
            $replies = Reply::where('device_id', $device->id)
                ->with('template')
                ->where('status', 1)
                ->where(function ($query) use ($message_caption, $driver) {
                    if ($driver === 'pgsql') {
                        // PostgreSQL version
                        $query->whereRaw("LOWER(?) = ANY(SELECT LOWER(unnest(string_to_array(keyword, ','))))", [$message_caption]);
                    } else {
                        // MySQL version
                        $query->whereRaw("FIND_IN_SET(LOWER(?), LOWER(keyword))", [strtolower($message_caption)]);
                    }
                })
                ->latest()
                ->get();

            // foreach ($replies as $key => $reply) {
            //     if ($reply->reply_type == 'unsubscribe' || strtolower($message_caption) === 'unsubscribe' || strtolower($message_caption) === 'stop promotions') {
            //         
            //         $blackList = new Blacklist();
            //         $blackList->device_id = $device->id;
            //         $blackList->sender_no = $from;
            //         $blackList->save();

            //         return;
            //     }

            //     if ($reply->match_type == 'equal') {
            //         $this->handle_message($device, $from, $reply);
            //         break;
            //     }
            // }

            // $contact = Contact::where('phone', $from)->where('user_id', $device->user_id)->first();
            // if (!$contact) {
            //     $contact = new Contact;
            //     $contact->user_id = $device->user_id;
            //     $contact->phone = $from;
            //     $contact->name = $name;
            //     $contact->save();
            // }

            foreach ($replies as $reply) {
                if (!empty($reply->agent_json)) {
                    // Log::info('agent_json get');

                    $agtData = json_decode($reply->agent_json, true);
                    $assignRule = $agtData['assign_rule']; // 'assign_all' or 'rotation'
                    $agentIds = collect($agtData['agents'])->pluck('agent_id')->toArray();

                    $contact = Contact::where('phone', $from)->where('user_id', $device->user_id)->first();
                    if (!$contact) {
                        continue;
                    }
                    $contId = $contact->id;

                    if ($assignRule === 'assign_all') {
                        // Existing logic: Assign all agents
                        foreach ($agentIds as $agentId) {
                            $agentExists = DB::table('users')->where('id', $agentId)->exists();
                            if ($agentExists) {
                                $relationExists = AgentContactRelationModel::where('agent_id', $agentId)
                                    ->where('contact_id', $contId)
                                    ->exists();

                                if (!$relationExists) {
                                    AgentContactRelationModel::create([
                                        'agent_id' => $agentId,
                                        'contact_id' => $contId
                                    ]);
                                }
                            }
                        }
                    } elseif ($assignRule === 'rotation_assign') {
                        // Rotation assign logic: Assign only one agent at a time
                        $lastAssigned = AgentContactRelationModel::whereIn('agent_id', $agentIds)
                            ->orderByDesc('created_at')
                            ->first();

                        // Find next agent to assign
                        $nextAgentId = null;
                        if ($lastAssigned) {
                            $lastIndex = array_search($lastAssigned->agent_id, $agentIds);
                            $nextIndex = ($lastIndex !== false && isset($agentIds[$lastIndex + 1])) ? $lastIndex + 1 : 0;
                            $nextAgentId = $agentIds[$nextIndex];
                        } else {
                            // First time assignment
                            $nextAgentId = $agentIds[0];
                        }

                        // Check if already assigned to this contact
                        $alreadyAssigned = AgentContactRelationModel::where('agent_id', $nextAgentId)
                            ->where('contact_id', $contId)
                            ->exists();

                        if (!$alreadyAssigned) {
                            AgentContactRelationModel::create([
                                'agent_id' => $nextAgentId,
                                'contact_id' => $contId
                            ]);
                        }
                    }
                }
            }

            // $tags = Tag::where('user_id', $device->user_id)->where('keyword', 'LIKE', '%' . $message_caption . '%')->latest()->get();
            $tags = Tag::where('user_id', $device->user_id)
                ->where(function ($query) use ($message_caption, $driver) {
                    if ($driver === 'pgsql') {
                        // PostgreSQL version
                        $query->whereRaw("LOWER(?) = ANY(SELECT LOWER(unnest(string_to_array(keyword, ','))))", [$message_caption]);
                    } else {
                        // MySQL version
                        $query->whereRaw("FIND_IN_SET(LOWER(?), LOWER(keyword))", [strtolower($message_caption)]);
                    }
                })
                ->latest()
                ->get();

            foreach ($tags as $key => $tag) {
                if ($tag->keyword == $message_caption) {
                    $this->store_contact_tag($tag->id, $from, $device->user_id);
                    break;
                }
            }
        }

        // $ext_hook_url = $device->meta ??  null;
        // if ($ext_hook_url) {
        //     Http::post($ext_hook_url, [
        //         "type" => "incoming_message",
        //         'device_phone' => $metadata_display_phone_number,
        //         'sender_no' => $from,
        //         'timestamp' => date("Y-m-d H:i:s", $time_stamp),
        //         'type' => $type,
        //         'body' => $message_caption,
        //         'media' => $file
        //     ]);
        // }
    }

    public function store_contact_tag($tagid, $phone, $user_id)
    {
        // dd($tagid);
        // dd($phone);
        // dd($user_id);
        $device = Device::where('user_id', $user_id)->first();
        // dd($device);
        $tag = Tag::where('id', $tagid)->first();
        // Log::info('tagis: ' . $tag);
        // dd($tag);
        if (!empty($tag->drip_id)) {
            // dd($tag->drip_id);
            $drip_campaign = DripCampaign::where('device_id', $device->id)
                ->where('id', $tag->drip_id)
                ->first();
            // dd($drip_campaign);
            $drip_data = json_decode($drip_campaign->drip_data, true);

            // $campaign_number = $phone;

            foreach ($drip_data as $drip) {
                // dd($drip['days']);
                //$drip['days'] = 1; then schedule today
                if ($drip['days'] === '1') {
                    $scheduled_on = now();
                } else {
                    //schedule time for each drip get from the days
                    $scheduled_on = now()->addDays($drip['days']);
                }
                //schedule time for each drip get from the days
                //$scheduled_on = now()->addDays($drip['days']);
                $uuid = (string) Str::uuid();
                $ip = request()->ip();
                $taskdata = [
                    'device_id' => (int)$device->id,
                    'created_by' => $device->user_id,
                    'scheduled_on' => $scheduled_on,
                    'task_url' => $drip['media_path'],
                    'campaign_name' => $drip_campaign->drip_name,
                    'templateId' => $drip['template'],
                    'task_type' => $drip['task_type'],
                    'parameters' => null,
                    'send_to_number' => $phone,
                    'text' => null,
                    'ip' => $ip,
                    'whatsapp_id' => $uuid,
                    'created_at' => now(),
                    'updated_at' => now()
                ];

                try {
                    DB::table('task')->insert($taskdata);
                } catch (\Exception $e) {
                    Log::error('Error inserting task data for auto assign tag drip send: ' . $e->getMessage());
                }

                try {
                    dispatch(new SendTaskJob($uuid))->delay(now()->setTimeFromTimeString($scheduled_on));
                } catch (\Exception $e) {
                    Log::error('Error dispatching the job for auto assign tag drip send: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
                }
            };
        }

        try {
            $contact = Contact::where('phone', $phone)->where('user_id', $user_id)->first();
            if ($contact) {
                //update
                $contact->tag_id = $tagid;
                $contact->save();
            } else {
                // create new
                $newContact = new Contact;
                $newContact->user_id = $user_id;
                $newContact->tag_id = $tagid;
                $newContact->phone = $phone;
                $newContact->save();
            }
            return response()->json(['message' => 'Tag added in contact'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Tag could not be added'], 500);
        }
    }

    public function send_fcm($device_uuid, $tokens, $from, $name, $message_caption, $file)
    {
        $ref_link = route('user.device.chats', $device_uuid);
        foreach ($tokens as $token) {
            Larafirebase::withTitle($from . '-' . $name)
                ->withBody($message_caption)
                ->withToken($token)
                ->sendNotification();
        }
    }

    /**
     * Refund credit to user when message fails to reach recipient
     *
     * @param Task $task
     * @param string $reason
     * @return void
     */
    public function refundCreditForFailedMessage($task, $reason = 'Message failed')
    {
        Log::info('getting in refund credit', [
            'task_id' => $task->task_id,
            'created_by' => $task->created_by,
            'reason' => $reason
        ]);
        try {
            // Get the user who sent the message
            $user = User::find($task->created_by);

            if (!$user) {
                Log::error("User not found for task ID: {$task->task_id}");
                return;
            }

            // Apply business rule: if user ID is not 1, refund credit
            if ($user->id != 1) {
                // Get the cost per message (business_initiated rate)
                $refundAmount = $user->business_initiated ?? 1; // Default to 1 if not set

                // Refund the credit to user's balance
                DB::transaction(function () use ($user, $refundAmount, $task, $reason) {
                    $user->balance += $refundAmount;
                    $user->save();

                    // Create order record for the refund transaction
                    // $order = new Order();
                    // $order->payment_id = 1;
                    // $order->plan_id = 1;
                    // $order->admin_id = 1; // System refund
                    // $order->user_id = $user->id;
                    // $order->gateway_id = 12; // Internal system gateway
                    // $order->amount = $refundAmount; // Positive amount for refund
                    // $order->tax = 0;
                    // $order->status = 1;
                    // $order->meta = "Credit refund for failed message - Task ID: {$task->task_id} - Reason: {$reason}";
                    // $order->save();

                    Log::info("Credit refunded successfully", [
                        'user_id' => $user->id,
                        'task_id' => $task->task_id,
                        'refund_amount' => $refundAmount,
                        'new_balance' => $user->balance,
                        'reason' => $reason
                    ]);
                });
            } else {
                Log::debug("No credit refund for user ID 1 (admin user)");
            }
        } catch (\Exception $e) {
            Log::error("Error refunding credit for failed message", [
                'task_id' => $task->task_id ?? 'unknown',
                'user_id' => $task->created_by ?? 'unknown',
                'error' => $e->getMessage(),
                'reason' => $reason
            ]);
        }
    }
}
