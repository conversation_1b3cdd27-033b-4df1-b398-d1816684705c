<?php

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

if (!function_exists('getFacebookTemplates')) {
    function getFacebookTemplates($waid, $token)
    {
        $version = env('FACEBOOK_VERSION', 'v18.0');
        $url = "https://graph.facebook.com/{$version}/{$waid}/message_templates";
        $headers = [
            'Authorization' => "Bearer {$token}",
        ];

        $allData = [];
        $after = null;
        $maxRetries = 3;
        $retryDelay = 2000; // milliseconds

        do {
            $attempt = 0;
            $success = false;
            
            while (!$success && $attempt < $maxRetries) {
                try {
                    $pagedUrl = $after ? $url . '?after=' . $after : $url;
                    $response = Http::timeout(30)->withHeaders($headers)->get($pagedUrl);
                    $success = true;
                } catch (\Exception $e) {
                    $attempt++;
                    if ($attempt >= $maxRetries) {
                        Log::error("Facebook API connection failed after {$maxRetries} attempts: " . $e->getMessage());
                        throw $e;
                    }
                    usleep($retryDelay * 1000); // Convert to microseconds
                }
            }
            
            if ($response->successful()) {
                $data = $response->json('data');

                // If data is blank, exit the loop
                if (empty($data)) {
                    break;
                }

                // Filter out unwanted templates
                $ignoredTemplates = [
                    'sample_flight_confirmation',
                    'sample_happy_hour_announcement',
                    'sample_movie_ticket_confirmation',
                    'sample_issue_resolution',
                    'sample_purchase_feedback',
                    'sample_shipping_confirmation',
                ];

                $filteredData = array_filter($data, function ($item) use ($ignoredTemplates) {
                    return !in_array($item['name'], $ignoredTemplates);
                });

                $allData = array_merge($allData, $filteredData);

                $paging = $response->json('paging');
                $after = $paging['cursors']['after'] ?? null;
            } else {
                $after = null;
            }
        } while ($after);

        return $allData;
    }
}